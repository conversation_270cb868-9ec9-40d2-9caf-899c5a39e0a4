<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Converter for boolean to color -->
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>

        <!-- Null to Visibility Converter -->
        <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect"
                          Color="Gray"
                          Direction="315"
                          ShadowDepth="3"
                          Opacity="0.3"
                          BlurRadius="5"/>

        <!-- Style for buttons -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F3F4F6"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="6,0"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E5E7EB"/>
                                <Setter Property="BorderBrush" Value="#9CA3AF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D1D5DB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FAFAFA">
        <!-- Header Section -->
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E5E5E5" BorderThickness="0,0,0,1" Padding="25,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1.2*"/>
                    <ColumnDefinition Width="2*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="تقرير الزيارة الميدانية"
                         FontSize="18" FontWeight="SemiBold" Foreground="#2C3E50" VerticalAlignment="Center"/>

                    <!-- رقم الزيارة -->
                    <Border Background="#F8F9FA" BorderBrush="#D1D5DB" BorderThickness="1" Padding="12,6" Margin="25,0,0,0" CornerRadius="4">
                        <TextBlock FontSize="13" FontWeight="Medium" Foreground="#374151" VerticalAlignment="Center">
                            <Run Text="رقم الزيارة: "/>
                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=---}" FontWeight="SemiBold"/>
                        </TextBlock>
                    </Border>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="معاينة الطباعة" Style="{StaticResource ModernButtonStyle}"
                           Click="PrintPreviewButton_Click"/>
                    <Button Content="طباعة" Style="{StaticResource ModernButtonStyle}"
                           Click="PrintReportButton_Click"/>
                    <Button Content="توثيق الرسائل" Style="{StaticResource ModernButtonStyle}"
                           Click="MessageDocumentationButton_Click"/>
                    <Button Content="التكليف" Style="{StaticResource ModernButtonStyle}"
                           Click="AssignmentButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30,25,30,15">
            <StackPanel>
                <!-- Report Content -->
                <Border Background="White" CornerRadius="6" Padding="40" Margin="0,0,0,25"
                       BorderBrush="#E5E7EB" BorderThickness="1">
                    <StackPanel>
                        <!-- Report Header -->
                        <Grid Margin="0,0,0,35">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="280"/>
                            </Grid.ColumnDefinitions>

                            <!-- Logo on the left -->
                            <Border Grid.Column="0" BorderBrush="#E5E7EB" BorderThickness="1" Padding="10,8" Background="#FAFAFA" CornerRadius="4">
                                <Image Source="pack://application:,,,/Icons/sfd.png" Width="50" Height="50" Stretch="Uniform"/>
                            </Border>

                            <!-- Empty space in center -->
                            <TextBlock Grid.Column="1"/>

                            <!-- Sector on the right -->
                            <Border Grid.Column="2" BorderBrush="#E5E7EB" BorderThickness="1" Padding="15,10" Background="#FAFAFA" CornerRadius="4">
                                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="13" FontWeight="Medium" Foreground="#374151">
                                    <Run Text="القطاع: "/>
                                    <Run Text="{Binding ReportData.SectorName, FallbackValue=الصحة والحماية الاجتماعية}" FontWeight="SemiBold"/>
                                </TextBlock>
                            </Border>
                        </Grid>

                        <!-- Report Title -->
                        <TextBlock Text="{Binding ReportData.ReportTitle}" FontSize="20" FontWeight="SemiBold"
                                 HorizontalAlignment="Center" Margin="0,0,0,25" Foreground="#1F2937"/>

                        <!-- Report Date -->
                        <TextBlock HorizontalAlignment="Center" Margin="0,0,0,35" FontSize="13" Foreground="#6B7280">
                        <Run Text="تاريخ التقرير: "/>
                        <Run Text="{Binding ReportData.ReportDate}" FontWeight="Medium"/>
                        </TextBlock>

                        <!-- Report Sections -->
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- القسم الأول: معلومات الزيارة -->
                            <Border Grid.Row="0" BorderBrush="#E5E7EB" BorderThickness="1" Margin="0,0,0,15" Background="White" Padding="20" CornerRadius="4">
                                <StackPanel>
                                    <TextBlock Text="معلومات الزيارة" FontWeight="SemiBold" FontSize="15"
                                         Foreground="#1F2937" Margin="0,0,0,15"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="1.5*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                            <TextBlock Text="الغرض من الزيارة:" FontWeight="Medium" FontSize="13"
                                                 Foreground="#374151" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ReportData.MissionPurpose}" FontSize="12"
                                                 Foreground="#6B7280" TextWrapping="Wrap" Background="#F9FAFB" Padding="12" LineHeight="18"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="طبيعة الزيارة:" FontWeight="Medium" FontSize="13"
                                                 Foreground="#374151" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ReportData.VisitNature}" FontSize="12"
                                                 Foreground="#6B7280" TextWrapping="Wrap" Background="#F9FAFB" Padding="12" LineHeight="18"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثاني: التواريخ -->
                            <Border Grid.Row="1" BorderBrush="#E5E7EB" BorderThickness="1" Margin="0,0,0,15" Background="White" Padding="20" CornerRadius="4">
                                <StackPanel>
                                    <TextBlock Text="تواريخ الزيارة" FontWeight="SemiBold" FontSize="15"
                                         Foreground="#1F2937" Margin="0,0,0,15"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="1.2*"/>
                                            <ColumnDefinition Width="1.2*"/>
                                            <ColumnDefinition Width="0.8*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                            <TextBlock Text="تاريخ المغادرة:" FontWeight="Medium" FontSize="13"
                                                 Foreground="#374151" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ReportData.DepartureDate}" FontSize="12"
                                                 Foreground="#6B7280" Background="#F9FAFB" Padding="12"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1" Margin="0,0,15,0">
                                            <TextBlock Text="تاريخ العودة:" FontWeight="Medium" FontSize="13"
                                                 Foreground="#374151" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ReportData.ReturnDate}" FontSize="12"
                                                 Foreground="#6B7280" Background="#F9FAFB" Padding="12"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="عدد الأيام:" FontWeight="Medium" FontSize="13"
                                                 Foreground="#374151" Margin="0,0,0,8"/>
                                            <TextBlock Text="{Binding ReportData.DaysCount}" FontSize="12"
                                                 Foreground="#6B7280" Background="#F9FAFB" Padding="12" TextAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثالث: خط السير -->
                            <Border Grid.Row="2" BorderBrush="#E5E7EB" BorderThickness="1" Margin="0,0,0,15" Background="White" Padding="20" CornerRadius="4">
                                <StackPanel>
                                    <TextBlock Text="خط السير" FontWeight="SemiBold" FontSize="15"
                                         Foreground="#1F2937" Margin="0,0,0,15"/>
                                    <Border BorderBrush="#E5E7EB" BorderThickness="1" Background="#FAFAFA" MinHeight="120" CornerRadius="4">
                                        <ItemsControl ItemsSource="{Binding ReportData.Itinerary}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="#F3F4F6" BorderThickness="0,0,0,1" Padding="15,12">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="110"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0" Text="{Binding DayNumber, StringFormat=اليوم {0}:}" FontWeight="Medium"
                                                                     Margin="0,0,20,0" Foreground="#374151" VerticalAlignment="Top" FontSize="13"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Plan}" FontSize="12" Foreground="#6B7280"
                                                                     TextWrapping="Wrap" VerticalAlignment="Top" LineHeight="18"/>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثالث والنصف: عدد الأيام ورقم المشروع -->
                            <Grid Grid.Row="3" Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="0.8*"/>
                                    <ColumnDefinition Width="1.2*"/>
                                </Grid.ColumnDefinitions>

                                <!-- عدد الأيام -->
                                <Border Grid.Column="0" BorderBrush="#E5E7EB" BorderThickness="1" Margin="0,0,10,0" Background="White" Padding="20" CornerRadius="4">
                                    <StackPanel>
                                        <TextBlock Text="عدد أيام الزيارة" FontWeight="Medium" FontSize="13"
                                                 Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,0,0,12"/>
                                        <Border Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="15,10" MinWidth="80">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding ReportData.DaysCount}" FontWeight="SemiBold" FontSize="18"
                                                         Foreground="#1F2937" HorizontalAlignment="Center"/>
                                                <TextBlock Text="يوم" FontWeight="Medium" FontSize="14"
                                                         Foreground="#6B7280" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- رقم المشروع -->
                                <Border Grid.Column="1" BorderBrush="#E5E7EB" BorderThickness="1" Margin="10,0,0,0" Background="White" Padding="20" CornerRadius="4">
                                    <StackPanel>
                                        <TextBlock Text="رقم المشروع" FontWeight="Medium" FontSize="13"
                                                 Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,0,0,12"/>
                                        <Border Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="15,10" MinWidth="120">
                                            <TextBlock Text="{Binding ReportData.ProjectNumber}" FontWeight="SemiBold" FontSize="16"
                                                     Foreground="#1F2937" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </Grid>



                            <!-- القسم الرابع: القائمين بالزيارة -->
                            <Border Grid.Row="4" BorderBrush="#E5E7EB" BorderThickness="1" Margin="0,0,0,15" Background="White" Padding="20" CornerRadius="4">
                                <StackPanel>
                                    <TextBlock Text="القائمين بالزيارة" FontWeight="Bold" FontSize="20"
                                         Foreground="#1565C0" Margin="0,0,0,15"/>
                                    <Border BorderBrush="#E5E7EB" BorderThickness="1" Background="#FAFAFA" CornerRadius="4">
                                        <ItemsControl ItemsSource="{Binding ReportData.Visitors}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="#F3F4F6" BorderThickness="0,0,0,1" Padding="15,12">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="25"/>
                                                                <ColumnDefinition Width="1.5*"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0" Text="•" FontWeight="Medium" Foreground="#9CA3AF" VerticalAlignment="Center" FontSize="14"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Name}" FontWeight="Medium" Margin="10,0,20,0"
                                                                     Foreground="#374151" VerticalAlignment="Center" FontSize="13"/>
                                                            <TextBlock Grid.Column="2" Text="{Binding Rank}" FontSize="12" Foreground="#6B7280"
                                                                     VerticalAlignment="Center"/>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Border>
                                </StackPanel>
                            </Border>

                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer ثابت في أسفل الصفحة -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="1"
                Width="900" Height="110"
                HorizontalAlignment="Center" VerticalAlignment="Bottom"
                Margin="0,0,0,25" Padding="15" CornerRadius="6">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- يعتمد -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBox Name="ApproverNameTextBox"
                            Text="م/محمد محمد الملحمي"
                            HorizontalAlignment="Center" FontSize="14" FontWeight="Medium"
                            Foreground="#1F2937" Margin="0,0,0,5"
                            Background="Transparent" BorderBrush="Transparent" BorderThickness="0"
                            TextAlignment="Center" IsReadOnly="False" Cursor="IBeam"/>
                    <TextBlock Text="يعتمد"
                             HorizontalAlignment="Center" FontSize="12"
                             Foreground="#6B7280" FontWeight="Medium"/>
                </StackPanel>

                <!-- مسئول الحركة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBox Name="MovementResponsibleTextBox"
                            Text="علي علي الغمدي"
                            HorizontalAlignment="Center" FontSize="14" FontWeight="Medium"
                            Foreground="#1F2937" TextWrapping="Wrap" TextAlignment="Center"
                            MaxWidth="200" Margin="0,0,0,5"
                            Background="Transparent" BorderBrush="Transparent" BorderThickness="0"
                            IsReadOnly="False" Cursor="IBeam"/>
                    <TextBlock Text="مسئول الحركة"
                             HorizontalAlignment="Center" FontSize="12"
                             Foreground="#6B7280" FontWeight="Medium"/>
                </StackPanel>

                <!-- المكلف بالمهمة -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBox Name="TaskAssigneeTextBox"
                            Text="علي علي أحمد الغمدي"
                            HorizontalAlignment="Center" FontSize="14" FontWeight="Medium"
                            Foreground="#1F2937" TextWrapping="Wrap" TextAlignment="Center"
                            MaxWidth="200" Margin="0,0,0,5"
                            Background="Transparent" BorderBrush="Transparent" BorderThickness="0"
                            IsReadOnly="False" Cursor="IBeam"/>
                    <TextBlock Text="المكلف بالمهمة"
                             HorizontalAlignment="Center" FontSize="12"
                             Foreground="#6B7280" FontWeight="Medium"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- صفحة بيان وإقرار -->
        <Border Grid.Row="3" Background="White" BorderBrush="Black" BorderThickness="1"
                Margin="20,20,20,20" Padding="30" CornerRadius="0"
                Visibility="Visible" x:Name="DeclarationPage">

            <!-- محتوى صفحة بيان وإقرار -->
            <StackPanel Orientation="Vertical">

                <!-- الرأس مع الشعار والعنوان -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- رقم المرجع -->
                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Padding="5">
                        <TextBlock Text="رقم مرجع: 911-13169" FontSize="10" HorizontalAlignment="Center"/>
                    </Border>

                    <!-- العنوان الرئيسي -->
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock Text="بيان وإقرار" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="ضريب المصنع والعلاقات العامة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>

                    <!-- شعار المؤسسة -->
                    <Border Grid.Column="2" BorderBrush="Black" BorderThickness="1" Width="60" Height="60" Background="#E0E0E0">
                        <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="8" TextWrapping="Wrap"
                                  HorizontalAlignment="Center" VerticalAlignment="Center" TextAlignment="Center"/>
                    </Border>
                </Grid>

                <!-- إقرار مزود الخدمة -->
                <Border BorderBrush="Black" BorderThickness="1" Margin="0,0,0,10">
                    <TextBlock Text="إقرار مزود الخدمة" FontSize="12" FontWeight="Bold"
                              HorizontalAlignment="Center" Padding="5" Background="#F0F0F0"/>
                </Border>

                <!-- النص الأساسي -->
                <TextBlock TextWrapping="Wrap" FontSize="11" Margin="0,0,0,15" TextAlignment="Justify">
                    <Run Text="أنا أو نحن مزود الخدمة صاحب الشركة رقم "/>
                    <Run Text="9-18093" FontWeight="Bold"/>
                    <Run Text=" بأني سلمت من الصندوق الاجتماعي للتنمية سعة من مدينة تعريب المصنع، وبخصوص تعاقدي مع العاملين في الصندوق فإنني أو نحن:"/>
                </TextBlock>

                <!-- العلاقات العائلية -->
                <Border BorderBrush="Black" BorderThickness="1" Margin="0,0,0,10">
                    <TextBlock Text="العلاقات العائلية" FontSize="12" FontWeight="Bold"
                              HorizontalAlignment="Center" Padding="5" Background="#F0F0F0"/>
                </Border>

                <!-- السؤال الأول -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="(1) لا يربطني علاقة عائلية مع أحد العاملين في الصندوق:" FontSize="11" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <CheckBox Content="نعم" Margin="0,0,20,0"/>
                        <CheckBox Content="لا"/>
                    </StackPanel>
                    <TextBlock Text="في حالة كانت الإجابة نعم يرجى ملء ما يلي:" FontSize="10"
                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- جدول المعلومات -->
                <Border BorderBrush="Black" BorderThickness="1" Margin="0,0,0,15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="30"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- رؤوس الأعمدة -->
                        <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                            <TextBlock Text="اسم العامل" Foreground="White" FontSize="10" FontWeight="Bold"
                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                            <TextBlock Text="فرع الصندوق" Foreground="White" FontSize="10" FontWeight="Bold"
                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <Border Grid.Row="0" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1" Background="Black">
                            <TextBlock Text="نوع العلاقة العائلية" Foreground="White" FontSize="10" FontWeight="Bold"
                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- الصفوف الفارغة -->
                        <Border Grid.Row="1" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1"/>
                        <Border Grid.Row="1" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1"/>
                        <Border Grid.Row="1" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1"/>

                        <Border Grid.Row="2" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1"/>
                        <Border Grid.Row="2" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1"/>
                        <Border Grid.Row="2" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1"/>

                        <Border Grid.Row="3" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0"/>
                        <Border Grid.Row="3" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0"/>
                        <Border Grid.Row="3" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,0"/>
                    </Grid>
                </Border>

                <!-- السؤال الثاني -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="(2) لا يربطني مصالح مباشرة أو غير مباشرة مع أحد العاملين في الصندوق:" FontSize="11" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <CheckBox Content="نعم" Margin="0,0,20,0"/>
                        <CheckBox Content="لا"/>
                    </StackPanel>
                    <TextBlock Text="في حالة كانت الإجابة نعم يرجى ملء ما يلي:" FontSize="10"
                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>

            </StackPanel>
        </Border>

        <!-- صفحة العقد الثانية -->
        <Border Background="White" CornerRadius="6" Padding="40" Margin="0,25,0,25"
               BorderBrush="#E5E7EB" BorderThickness="1" x:Name="ContractPage">
            <StackPanel>
                <!-- عنوان صفحة العقد -->
                <TextBlock Text="عقد استئجار وسيلة نقل" FontSize="18" FontWeight="Bold"
                          HorizontalAlignment="Center" Margin="0,0,0,30"/>

                <!-- مقدمة العقد -->
                <TextBlock Text="{Binding ContractTemplate.ContractIntroduction}" FontSize="12"
                          TextWrapping="Wrap" Margin="0,0,0,20" TextAlignment="Justify"/>

                <!-- الطرف الأول -->
                <TextBlock Text="الطرف الأول:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.FirstPartyTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- الطرف الثاني -->
                <TextBlock Text="الطرف الثاني:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.SecondPartyTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- مواصفات المركبة -->
                <TextBlock Text="مواصفات المركبة:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.VehicleSpecsTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- الغرض من العقد -->
                <TextBlock Text="الغرض من العقد:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.PurposeTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- مدة العقد -->
                <TextBlock Text="مدة العقد:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.DurationTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- القيمة الإيجارية -->
                <TextBlock Text="القيمة الإيجارية:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                <Border BorderBrush="#E5E7EB" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#FAFAFA">
                    <TextBlock Text="{Binding ContractTemplate.PriceTemplate}" FontSize="12"
                              TextWrapping="Wrap" LineHeight="20"/>
                </Border>

                <!-- التوقيعات -->
                <Grid Margin="0,30,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- توقيع الطرف الأول -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock Text="الطرف الأول" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Text="(مالك السيارة)" FontSize="10" Margin="0,0,0,10" Foreground="#666"/>

                        <!-- اسم السائق -->
                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName, FallbackValue='عبده علي محمد عايض'}"
                                  FontWeight="Bold" FontSize="12" Margin="0,0,0,3" HorizontalAlignment="Center"/>

                        <!-- رقم التلفون تحت الاسم -->
                        <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber, FallbackValue='0501234567'}"
                                  FontSize="11" Margin="0,0,0,15" Foreground="#1565C0" HorizontalAlignment="Center"/>

                        <Border BorderBrush="Black" BorderThickness="0,0,0,1" Width="150" Height="30"/>
                        <TextBlock Text="التاريخ: ___________" Margin="0,10,0,0"/>
                    </StackPanel>

                    <!-- توقيع الطرف الثاني -->
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock Text="توقيع الطرف الثاني" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Border BorderBrush="Black" BorderThickness="0,0,0,1" Width="150" Height="30"/>
                        <TextBlock Text="التاريخ: ___________" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- فوتر صفحة العقد مع بيانات السائق الفائز -->
                <Border BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Margin="0,40,0,0" Padding="0,15,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- بيانات السائق الفائز -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Right">
                            <TextBlock Text="الطرف الأول:" FontWeight="Bold" FontSize="12" Margin="0,0,0,8"/>

                            <!-- اسم السائق -->
                            <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName, FallbackValue='غير محدد'}"
                                      FontWeight="Bold" FontSize="13" Margin="0,0,0,3"
                                      HorizontalAlignment="Center"/>

                            <!-- رقم التلفون تحت الاسم مباشرة -->
                            <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber, FallbackValue='لم يتم تحديد رقم التلفون'}"
                                      FontSize="11" Margin="0,0,0,8" Foreground="#1565C0"
                                      HorizontalAlignment="Center"/>

                            <!-- نوع المركبة -->
                            <TextBlock FontSize="11" Margin="0,2">
                                <Run Text="نوع المركبة: "/>
                                <Run Text="{Binding ReportData.WinnerDriver.VehicleType, FallbackValue='غير محدد'}" FontWeight="Bold"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- معلومات إضافية -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Left">
                            <TextBlock Text="معلومات العقد:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                            <TextBlock FontSize="11" Margin="0,2">
                                <Run Text="رقم الزيارة: "/>
                                <Run Text="{Binding ReportData.VisitNumber, FallbackValue='غير محدد'}" FontWeight="Bold"/>
                            </TextBlock>
                            <TextBlock FontSize="11" Margin="0,2">
                                <Run Text="عدد الأيام: "/>
                                <Run Text="{Binding ReportData.DaysCount, FallbackValue='0'}" FontWeight="Bold"/>
                                <Run Text=" يوم"/>
                            </TextBlock>
                            <TextBlock FontSize="11" Margin="0,2">
                                <Run Text="المبلغ الإجمالي: "/>
                                <Run Text="{Binding ReportData.WinnerDriver.WinningPrice, StringFormat='{}{0:N0} ريال', FallbackValue='غير محدد'}" FontWeight="Bold"/>
                            </TextBlock>
                        </StackPanel>
                    </Grid>
                </Border>

            </StackPanel>
        </Border>
    </Grid>
</UserControl>